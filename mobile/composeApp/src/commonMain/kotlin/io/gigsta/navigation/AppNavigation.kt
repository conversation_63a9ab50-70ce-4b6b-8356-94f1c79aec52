package io.gigsta.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import io.gigsta.presentation.home.HomeScreen
import io.gigsta.presentation.profile.ProfileScreen

@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController()
) {
    NavHost(
        navController = navController,
        startDestination = Screen.Home
    ) {
        composable<Screen.Home> {
            HomeScreen(
                onNavigateToProfile = {
                    navController.navigate(Screen.Profile)
                },
                onCreateEmailApplication = {
                    // TODO: Navigate to email application creation
                },
                onCreateApplicationLetter = {
                    // TODO: Navigate to application letter creation
                },
                onCreateATSResume = {
                    // TODO: Navigate to ATS resume creation
                },
                onCreateJobMatching = {
                    // TODO: Navigate to job matching analysis
                }
            )
        }
        
        composable<Screen.Profile> {
            ProfileScreen(
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }
    }
}
