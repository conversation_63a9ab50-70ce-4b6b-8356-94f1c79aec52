package io.gigsta.presentation.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.gigsta.domain.usecase.GetCurrentUserUseCase
import io.gigsta.domain.usecase.GetEmailHistoryUseCase
import io.gigsta.domain.usecase.GetLetterHistoryUseCase
import io.gigsta.domain.usecase.GetUserProfileUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class HomeViewModel(
    private val getCurrentUserUseCase: GetCurrentUserUseCase,
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val getEmailHistoryUseCase: GetEmailHistoryUseCase,
    private val getLetterHistoryUseCase: GetLetterHistoryUseCase
) : ViewModel() {

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    init {
        loadData()
    }

    fun loadData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                // Get current user
                val userResult = getCurrentUserUseCase()
                if (userResult.isFailure) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "Failed to load user data"
                    )
                    return@launch
                }

                val user = userResult.getOrNull()
                if (user == null) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "User not authenticated"
                    )
                    return@launch
                }

                _uiState.value = _uiState.value.copy(user = user)

                // Load user profile and history data in parallel
                val profileResult = getUserProfileUseCase(user.id)
                val emailHistoryResult = getEmailHistoryUseCase(user.id)
                val letterHistoryResult = getLetterHistoryUseCase(user.id)

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    userProfile = profileResult.getOrNull(),
                    emailHistory = emailHistoryResult.getOrElse { emptyList() },
                    letterHistory = letterHistoryResult.getOrElse { emptyList() }
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "An unexpected error occurred"
                )
            }
        }
    }

    fun refresh() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isRefreshing = true)
            loadData()
            _uiState.value = _uiState.value.copy(isRefreshing = false)
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}
