package io.gigsta.presentation.home

import io.gigsta.domain.model.EmailHistoryItem
import io.gigsta.domain.model.LetterHistoryItem
import io.gigsta.domain.model.User
import io.gigsta.domain.model.UserProfile

data class HomeUiState(
    val isLoading: Boolean = false,
    val user: User? = null,
    val userProfile: UserProfile? = null,
    val emailHistory: List<EmailHistoryItem> = emptyList(),
    val letterHistory: List<LetterHistoryItem> = emptyList(),
    val error: String? = null,
    val isRefreshing: Boolean = false
)
