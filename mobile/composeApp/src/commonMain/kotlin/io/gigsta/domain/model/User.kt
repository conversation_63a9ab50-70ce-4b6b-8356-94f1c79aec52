package io.gigsta.domain.model

import kotlinx.serialization.Serializable

@Serializable
data class User(
    val id: String,
    val email: String,
    val fullName: String? = null
)

@Serializable
data class UserProfile(
    val id: String,
    val tokens: Int? = null,
    val resumeFileName: String? = null,
    val realFileName: String? = null,
    val resumeUrl: String? = null,
    val resumeUploadedAt: String? = null
)
