package io.gigsta.di

import io.gigsta.data.remote.api.ApiClient
import io.gigsta.data.repository.AuthRepositoryImpl
import io.gigsta.data.repository.EmailRepositoryImpl
import io.gigsta.data.repository.LetterRepositoryImpl
import io.gigsta.domain.repository.AuthRepository
import io.gigsta.domain.repository.EmailRepository
import io.gigsta.domain.repository.LetterRepository
import io.gigsta.domain.usecase.GetCurrentUserUseCase
import io.gigsta.domain.usecase.GetEmailHistoryUseCase
import io.gigsta.domain.usecase.GetLetterHistoryUseCase
import io.gigsta.domain.usecase.GetUserProfileUseCase
import io.gigsta.presentation.home.HomeViewModel
import io.gigsta.presentation.profile.ProfileViewModel
import org.koin.core.module.dsl.viewModel
import org.koin.dsl.module

val appModule = module {
    // API Client
    single { ApiClient() }
    
    // Repositories
    single<AuthRepository> { AuthRepositoryImpl(get()) }
    single<EmailRepository> { EmailRepositoryImpl(get()) }
    single<LetterRepository> { LetterRepositoryImpl(get()) }
    
    // Use Cases
    single { GetCurrentUserUseCase(get()) }
    single { GetUserProfileUseCase(get()) }
    single { GetEmailHistoryUseCase(get()) }
    single { GetLetterHistoryUseCase(get()) }
    
    // ViewModels
    viewModel { HomeViewModel(get(), get(), get(), get()) }
    viewModel { ProfileViewModel(get(), get()) }
}
