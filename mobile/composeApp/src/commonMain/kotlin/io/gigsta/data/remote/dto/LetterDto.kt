package io.gigsta.data.remote.dto

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class LetterDto(
    val id: String,
    @SerialName("plain_text")
    val plainText: String,
    @SerialName("design_html")
    val designHtml: String? = null,
    @SerialName("template_id")
    val templateId: String,
    @SerialName("created_at")
    val createdAt: String
)
