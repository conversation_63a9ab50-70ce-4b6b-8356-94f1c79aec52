package io.gigsta.data.mapper

import io.gigsta.data.remote.dto.UserDto
import io.gigsta.data.remote.dto.UserProfileDto
import io.gigsta.domain.model.User
import io.gigsta.domain.model.UserProfile

fun UserDto.toDomain(): User {
    return User(
        id = id,
        email = email,
        fullName = userMetadata?.fullName
    )
}

fun UserProfileDto.toDomain(): UserProfile {
    return UserProfile(
        id = id,
        tokens = tokens,
        resumeFileName = resumeFileName,
        realFileName = realFileName,
        resumeUrl = resumeUrl,
        resumeUploadedAt = resumeUploadedAt
    )
}
