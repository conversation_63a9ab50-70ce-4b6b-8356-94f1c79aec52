package io.gigsta.data.repository

import io.gigsta.data.mapper.toDomain
import io.gigsta.data.remote.api.ApiClient
import io.gigsta.data.remote.dto.LetterDto
import io.gigsta.domain.model.LetterHistoryItem
import io.gigsta.domain.repository.LetterRepository

class LetterRepositoryImpl(
    private val apiClient: ApiClient
) : LetterRepository {

    override suspend fun getLetterHistory(userId: String): Result<List<LetterHistoryItem>> {
        return try {
            // Mock data for testing - replace with actual API call
            // When connecting to real API, use:
            // val response = apiClient.httpClient.get("${apiClient.baseUrl}/letters?user_id=$userId&status=done")
            // val letterDtos = response.body<List<LetterDto>>()
            // Result.success(letterDtos.map { it.toDomain() })

            val mockLetters = listOf(
                LetterHistoryItem(
                    id = "letter-1",
                    plainText = "Dear Hiring Manager, I am writing to apply for the Software Engineer position at TechCorp. With over 3 years of experience in mobile development and a strong background in Kotlin and React Native, I am confident that I would be a valuable addition to your team...",
                    designHtml = "<html><body><p>Dear Hiring Manager,</p><p>I am writing to apply for the Software Engineer position at TechCorp...</p></body></html>",
                    templateId = "professional",
                    templateName = "Professional",
                    createdAt = "2024-01-15T11:00:00Z"
                ),
                LetterHistoryItem(
                    id = "letter-2",
                    plainText = "Dear Ms. Smith, I am excited to submit my application for the Frontend Developer role at InnovateTech. My passion for creating user-friendly interfaces and my experience with modern web technologies make me an ideal candidate...",
                    designHtml = "<html><body><p>Dear Ms. Smith,</p><p>I am excited to submit my application for the Frontend Developer role...</p></body></html>",
                    templateId = "modern",
                    templateName = "Modern",
                    createdAt = "2024-01-14T16:45:00Z"
                ),
                LetterHistoryItem(
                    id = "letter-3",
                    plainText = "To Whom It May Concern, I would like to express my interest in the Mobile Developer position at StartupXYZ. My experience in building cross-platform mobile applications and my dedication to clean code practices...",
                    designHtml = null,
                    templateId = "plain-text",
                    templateName = "Plain Text",
                    createdAt = "2024-01-13T13:30:00Z"
                )
            )
            Result.success(mockLetters)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
