package io.gigsta.data.repository

import io.gigsta.data.mapper.toDomain
import io.gigsta.data.remote.api.ApiClient
import io.gigsta.data.remote.dto.EmailDto
import io.gigsta.domain.model.EmailHistoryItem
import io.gigsta.domain.repository.EmailRepository

class EmailRepositoryImpl(
    private val apiClient: ApiClient
) : EmailRepository {

    override suspend fun getEmailHistory(userId: String): Result<List<EmailHistoryItem>> {
        return try {
            // Mock data for testing - replace with actual API call
            // When connecting to real API, use:
            // val response = apiClient.httpClient.get("${apiClient.baseUrl}/emails?user_id=$userId")
            // val emailDtos = response.body<List<EmailDto>>()
            // Result.success(emailDtos.map { it.toDomain() })

            val mockEmails = listOf(
                EmailHistoryItem(
                    id = "email-1",
                    subject = "Application for Software Developer Position",
                    body = "Dear Hiring Manager, I am writing to express my interest in the Software Developer position at your company. With my experience in mobile development and passion for creating innovative solutions...",
                    createdAt = "2024-01-15T10:30:00Z"
                ),
                EmailHistoryItem(
                    id = "email-2",
                    subject = "Follow-up on Frontend Developer Application",
                    body = "Dear Ms. Johnson, I wanted to follow up on my application for the Frontend Developer position submitted last week. I am very excited about the opportunity to contribute to your team...",
                    createdAt = "2024-01-14T14:20:00Z"
                ),
                EmailHistoryItem(
                    id = "email-3",
                    subject = "Application for Mobile App Developer Role",
                    body = "Hello, I am reaching out regarding the Mobile App Developer position posted on your careers page. My background in Kotlin and React Native makes me a strong candidate...",
                    createdAt = "2024-01-13T09:15:00Z"
                )
            )
            Result.success(mockEmails)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
