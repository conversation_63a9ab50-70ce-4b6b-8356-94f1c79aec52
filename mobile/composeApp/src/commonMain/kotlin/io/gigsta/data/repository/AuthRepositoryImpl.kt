package io.gigsta.data.repository

import io.gigsta.data.mapper.toDomain
import io.gigsta.data.remote.api.ApiClient
import io.gigsta.data.remote.dto.UserDto
import io.gigsta.data.remote.dto.UserProfileDto
import io.gigsta.domain.model.User
import io.gigsta.domain.model.UserProfile
import io.gigsta.domain.repository.AuthRepository
import io.ktor.client.call.*

class AuthRepositoryImpl(
    private val apiClient: ApiClient
) : AuthRepository {

    override suspend fun getCurrentUser(): Result<User?> {
        return try {
            // Mock data for testing - replace with actual API call
            // When connecting to real API, use:
            // val response = apiClient.httpClient.get("${apiClient.baseUrl}/auth/user")
            // val userDto = response.body<UserDto>()
            // Result.success(userDto.toDomain())

            val mockUser = User(
                id = "mock-user-id",
                email = "<EMAIL>",
                fullName = "John Doe"
            )
            Result.success(mockUser)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getUserProfile(userId: String): Result<UserProfile?> {
        return try {
            // Mock data for testing - replace with actual API call
            // When connecting to real API, use:
            // val response = apiClient.httpClient.get("${apiClient.baseUrl}/profiles/$userId")
            // val profileDto = response.body<UserProfileDto>()
            // Result.success(profileDto.toDomain())

            val mockProfile = UserProfile(
                id = userId,
                tokens = 25,
                resumeFileName = "resume.pdf",
                realFileName = "John_Doe_Resume.pdf",
                resumeUrl = "https://example.com/resume.pdf",
                resumeUploadedAt = "2024-01-15T10:30:00Z"
            )
            Result.success(mockProfile)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun signOut(): Result<Unit> {
        return try {
            // Mock implementation - replace with actual API call
            // When connecting to real API, use:
            // val response = apiClient.httpClient.post("${apiClient.baseUrl}/auth/signout")
            // Result.success(Unit)

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
