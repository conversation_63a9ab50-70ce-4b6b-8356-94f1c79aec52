package io.gigsta.data.mapper

import io.gigsta.data.remote.dto.LetterDto
import io.gigsta.domain.model.LetterHistoryItem

// Template mapping - in a real app, this would come from the API or be stored locally
private val templateNames = mapOf(
    "plain-text" to "Plain Text",
    "modern" to "Modern",
    "classic" to "Classic",
    "professional" to "Professional"
)

fun LetterDto.toDomain(): LetterHistoryItem {
    return LetterHistoryItem(
        id = id,
        plainText = plainText,
        designHtml = designHtml,
        templateId = templateId,
        templateName = templateNames[templateId] ?: "Unknown Template",
        createdAt = createdAt
    )
}
