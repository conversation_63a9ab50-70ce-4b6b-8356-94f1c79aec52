package io.gigsta.data.remote.dto

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class UserDto(
    val id: String,
    val email: String,
    @SerialName("user_metadata")
    val userMetadata: UserMetadataDto? = null
)

@Serializable
data class UserMetadataDto(
    @SerialName("full_name")
    val fullName: String? = null
)

@Serializable
data class UserProfileDto(
    val id: String,
    val tokens: Int? = null,
    @SerialName("resume_file_name")
    val resumeFileName: String? = null,
    @SerialName("real_file_name")
    val realFileName: String? = null,
    @SerialName("resume_url")
    val resumeUrl: String? = null,
    @SerialName("resume_uploaded_at")
    val resumeUploadedAt: String? = null
)
