package io.gigsta

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import io.gigsta.di.appModule
import io.gigsta.navigation.AppNavigation
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.KoinApplication

@Composable
@Preview
fun App() {
    KoinApplication(application = {
        modules(appModule)
    }) {
        MaterialTheme {
            AppNavigation()
        }
    }
}